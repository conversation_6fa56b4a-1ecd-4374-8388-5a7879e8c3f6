import ZnBundleProduct from '#models/zn_bundle_product'
import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnCart from '#models/zn_cart'
import ZnCartItem from '#models/zn_cart_item'
import ZnCartSection from '#models/zn_cart_section'
import ZnProductVariant from '#models/zn_product_variant'
import CheckoutService from '#services/checkout_service'
import db from '@adonisjs/lucid/services/db'
import { AddBundleToCartService } from './add_bundle_to_cart_service.js'

export class ShopCartService {
  public async mergeCart({ userId, cartId }: { userId?: string; cartId?: string }) {
    let userCart
    let guestCart
    if (userId) {
      userCart = await ZnCart.query().where('userId', userId).first()
    }

    if (cartId) {
      guestCart = await ZnCart.query().where('id', cartId).first()
    }

    // Create a new cart for the user if it doesn't exist
    if (!guestCart) {
      guestCart = await ZnCart.create({})
    }

    // If the user NOT authenticated, return guestCart
    if (!userId) {
      return guestCart
    }
    if (userCart?.id === guestCart?.id) {
      return userCart
    }

    // If the user cart doesn't exist, assign the guest cart to the user
    if (!userCart) {
      await guestCart.merge({ userId }).save()
      userCart = guestCart
    } else {
      // If the user cart exists, merge the guest cart items into the user's cart
      await ZnCartSection.query().where('cartId', guestCart.id).update({ cartId: userCart.id })
      await guestCart.delete()
    }

    return userCart
  }

  async addItemToCart(payload: IAddItemToCart) {
    const cart = await this.mergeCart({ userId: payload.userId, cartId: payload.cartId })
    const cartId = cart.id
    const variant = await ZnProductVariant.query()
      .where('id', payload.variantId)
      .preload('image')
      .preload('product', (query) => {
        query.preload('image')
      })
      .firstOrFail()

    if (!variant.availableForSale) {
      throw new Error('Variant not available for sale')
    }

    // Get All cart sections NOT bundle
    const cartSectionIds = (
      await ZnCartSection.query().where('cartId', cartId).has('cartItems', '=', 1).select('id')
    ).map((cs) => cs.id)

    // Check if the variant already exists in the cart
    const cartItemExist = await ZnCartItem.query()
      .whereIn('cartSectionId', cartSectionIds)
      .where('variantId', variant.id)
      .preload('cartSection')
      .first()

    // If the variant does not exist in the cart, create a new cart item
    const rawPrice = variant.compareAtPrice
    const price = variant.price

    let cartSection

    // If the variant already exists in the cart, update the quantity
    if (cartItemExist) {
      const cartSectionExist = cartItemExist.cartSection
      const quantity = Number(cartSectionExist.quantity) + Number(payload.quantity)
      cartSection = await this.updateQuantityCartSection({
        cartSection: cartSectionExist,
        quantity,
      })
    } else {
      // If the variant does not exist in the cart, create a new cart item
      cartSection = await ZnCartSection.create({
        cartId,
        quantity: payload.quantity,
        rawTotal: rawPrice ? rawPrice * payload.quantity : null,
        total: price * payload.quantity,
        rawPrice,
        price,
      })

      const data = {
        cartSectionId: cartSection.id,
        productId: variant.productId,
        variantId: variant.id,
        quantity: 1,
        rawPrice: variant.compareAtPrice || variant.price,
        price: variant.price,
        productName: variant.product.title,
        variantName: variant.title,
        image: variant.image?.src || variant.product.image?.src,
        sku: variant.sku,
      }
      await cartSection.related('cartItems').create(data)
    }

    if (cartSection) {
      // Refresh cart section with cart items
      await cartSection.load('cartItems')
      await cartSection.refresh()
    }

    return cartSection
  }

  async addBundleToCart(payload: IAddBundleToCart) {
    const cart = await this.mergeCart({ userId: payload.userId, cartId: payload.cartId })

    const bundle = await ZnBundleProduct.query()
      .where('id', payload.bundleId)
      .preload('items', (query) => {
        query
          .preload('product', (query) => {
            query.preload('image')
          })
          .preload('variants', (query) => {
            query.preload('image')
          })
      })
      .preload('collections', (query) => {
        query.preload('items', (query) => {
          query.preload('variants', (query) => {
            query.preload('image')
          })
        })
      })
      .preload('discounts')
      .firstOrFail()

    const addBundleToCartService = new AddBundleToCartService()
    return await addBundleToCartService.handle({
      cart,
      bundle,
      payloadItems: payload.items,
      payloadCollections: payload.collections,
      payloadQuantity: payload.quantity,
    })
  }

  async updateQuantityCartSection({
    cartSection,
    quantity,
  }: {
    cartSection: ZnCartSection
    quantity: number
  }): Promise<ZnCartSection | null> {
    if (quantity === 0) {
      await this.deleteCartSection(cartSection)
      return null
    }
    // Get original quantity, total, rawTotal
    const price = Number(cartSection.price)
    const rawPrice = cartSection.rawPrice ? Number(cartSection.rawPrice) : null

    // Update quantity
    await cartSection
      .merge({
        rawTotal: rawPrice ? rawPrice * quantity : null,
        total: price * quantity,
        quantity,
      })
      .save()

    // Check and apply bundle
    const shopCartService = new ShopCartService()
    return await shopCartService.checkAndApplyBundle(cartSection)
  }

  async deleteCartSection(cartSection: ZnCartSection): Promise<void> {
    await cartSection.related('cartItems').query().delete()
    await cartSection.delete()
  }

  async checkAndApplyBundle(cartSection: ZnCartSection): Promise<ZnCartSection> {
    await cartSection.load('cartItems', (query) => {
      query.preload('variant')
    })

    // If cart section has more than 1 item, return it (CHECK APPLY Buy more get more)
    if (cartSection.cartItems.length > 1) {
      return cartSection
    }

    await cartSection.load('bundle', (query) => {
      query.preload('discounts')
    })

    // If cart section does not have bundle, Find bundle for it
    return await this.findAndApplyNewBundle(cartSection)
  }

  async removeBundleToCartSection(cartSection: ZnCartSection) {
    await cartSection.load('cartItems', (query) => {
      query.preload('variant')
    })
    const subtotal = cartSection.cartItems.reduce(
      (acc, ci) => acc + ci.variant.price * ci.quantity,
      0
    )
    await cartSection
      .merge({
        price: subtotal,
        rawPrice: subtotal,
        total: subtotal * cartSection.quantity,
        rawTotal: subtotal * cartSection.quantity,
        bundleId: null,
        title: '',
      })
      .save()

    for (const cartItem of cartSection.cartItems) {
      await cartItem
        .merge({
          price: cartItem.variant.price,
          rawPrice: cartItem.variant.price,
          bundleDiscountId: null,
        })
        .save()
    }
    await cartSection.refresh()
    await cartSection.load('bundle')
    await cartSection.load('cartItems')

    return cartSection
  }

  private async findAndApplyNewBundle(cartSection: ZnCartSection) {
    let bundle = cartSection.bundle as ZnBundleProduct | null
    const quantity = cartSection.quantity

    if (!bundle) {
      bundle = await this.getBundleByVariantId(cartSection.cartItems[0].variant.id)
      if (!bundle) {
        return cartSection
      }
    } else {
      await cartSection.load('bundle', (query) => {
        query.preload('discounts').preload('items', (query: any) => {
          query.preload('product').preload('variants')
        })
      })
    }
    const resultDiscount = this.getDiscountForBundleBuyMoreGetMore(bundle, quantity)
    const subtotal = cartSection.cartItems.reduce(
      (acc, ci) => acc + ci.variant.price * ci.quantity,
      0
    )
    // If NOT found discount Then Remove bundle
    if (!resultDiscount) {
      return await this.removeBundleToCartSection(cartSection)
    }

    const checkoutService = new CheckoutService()
    // Calculate discount
    const result = checkoutService.calculatorDiscountForVariant({
      discount: resultDiscount,
      subTotal: subtotal,
    })
    // Calculate price
    const price = checkoutService.calculatorDiscount(result, subtotal)
    await cartSection
      .merge({
        bundleId: bundle.id,
        title: bundle.title,
        price,
        rawPrice: subtotal,
        total: price * quantity,
        rawTotal: subtotal * quantity,
      })
      .save()

    for (const cartItem of cartSection.cartItems) {
      const variant = cartItem.variant
      const price = checkoutService.calculatorDiscount(result, variant.price)
      await cartItem
        .merge({
          price,
          rawPrice: variant.price,
          bundleDiscountId: resultDiscount.id,
        })
        .save()
    }

    return cartSection
  }

  public getDiscountForBundleBuyMoreGetMore(
    bundle: ZnBundleProduct,
    quantity: number
  ): ZnBundleProductDiscount | undefined {
    let resultDiscount
    let distance = Infinity
    for (const discount of bundle.discounts) {
      const discountQuantity = discount.quantity
      if (discountQuantity <= quantity) {
        if (quantity - discountQuantity < distance) {
          resultDiscount = discount
          distance = quantity - discountQuantity
        }
      }
    }

    return resultDiscount
  }

  private async getBundleByVariantId(variantId: string): Promise<ZnBundleProduct | null> {
    const query = await db.rawQuery(`
      SELECT b.id as bundleId
      FROM
          zn_bundle_products b
          LEFT JOIN zn_bundle_product_items bi ON b.id = bi.bundleProductId
          LEFT JOIN zn_bundle_product_item_variants biv ON bi.id = biv.bundleProductItemId
      WHERE
          b.type = 'bogo'
          AND biv.variantId = '${variantId}'
    `)
    const bundleId = query?.[0]?.[0]?.bundleId

    if (!bundleId) {
      return null
    }

    const bundle = await ZnBundleProduct.query()
      .where('id', bundleId)
      .preload('discounts')
      .preload('items', (query) => {
        query.preload('product')
        query.preload('variants')
      })
      .first()

    return bundle
  }

  async checkAllItemsAvailableForSale(cartSectionId: string): Promise<boolean> {
    const cartSection = await ZnCartSection.query()
      .where('id', cartSectionId)
      .preload('cartItems', (query) => {
        query.preload('variant').preload('product')
      })
      .first()

    if (!cartSection) {
      return false
    }

    // Check if all items are available for sale
    for (const cartItem of cartSection.cartItems) {
      // Check if variant is not available for sale
      if (!cartItem.variant.availableForSale) {
        return false
      }
      // Check if product is not active
      if (cartItem.product.status !== 'active') {
        return false
      }
      // Check if product is gift (not allow to add to cart)
      if (cartItem.product.isGift) {
        return false
      }
    }

    return true
  }
}
