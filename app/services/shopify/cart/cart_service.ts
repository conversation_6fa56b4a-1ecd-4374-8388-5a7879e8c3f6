import ZnBundleProductDiscount from '#models/zn_bundle_product_discount'
import ZnProductVariant from '#models/zn_product_variant'
import ZnShopifyDraftOrder from '#models/zn_shopify_draft_order'
import CheckoutService from '#services/checkout_service'
import { DateTime } from 'luxon'
import { formatOrderCart } from '../../../../services/shopify/cart/cart_format.js'
import {
  IDraftOrderUpdateAddress,
  IDraftOrderUpdateDiscountCodes,
  IDraftOrderUpdateLineItems,
  IDraftOrderUpdateNote,
  ILineItem,
} from '../order/draft_order.js'
import { ShopifyDraftOrderService } from '../order/draft_order_service.js'
import { ICreateCartParams, IGetLineItemsParams } from './cart_type.js'

type IItem = {
  quantity: number
  fastBundleDiscountId: number
  variantId: string
  shopifyVariantId: string
}

export class CartService {
  async getLineItems({ lineItems: defaultLineItems }: IGetLineItemsParams) {
    const checkoutService = new CheckoutService()
    const variantIds = defaultLineItems.map((item) => item.variantId)
    const variants = await ZnProductVariant.query().whereIn('id', variantIds)
    const lineItems = [] as ILineItem[]
    // Create the lines (LineItem)
    const items = defaultLineItems
      .map((item) => {
        const variant = variants.find((v) => item.variantId === v.id)
        if (!variant || !variant.availableForSale) {
          return null
        }
        return {
          quantity: item.quantity || 0,
          fastBundleDiscountId: item.fastBundleDiscountId,
          variantId: variant.id,
          shopifyVariantId: variant.shopifyVariantId,
        }
      })
      .filter(Boolean) as IItem[]

    const lineItemNotBundles = items.filter((i) => !i.fastBundleDiscountId)
    for (const item of lineItemNotBundles) {
      if (item.variantId && !item.shopifyVariantId) {
        const variant = await ZnProductVariant.findOrFail(item.variantId)
        item.shopifyVariantId = variant.shopifyVariantId
      }
      const lineItem = lineItems.find((i) => i.variantId === item.shopifyVariantId)
      if (lineItem) {
        lineItem.quantity += item.quantity
      } else {
        lineItems.push({
          quantity: item.quantity,
          variantId: item.shopifyVariantId,
          appliedDiscount: undefined,
        })
      }
    }

    // Process the line items that are BUNDLES
    const lineItemBundles = items.filter((i) => i.fastBundleDiscountId)
    const fastBundleDiscountIds = [...new Set(lineItemBundles.map((i) => i.fastBundleDiscountId))]
    for (const fastBundleDiscountId of fastBundleDiscountIds) {
      const dataLineItems = lineItemBundles
        .filter((i) => i.fastBundleDiscountId === fastBundleDiscountId && i.variantId)
        .map((i) => ({ variantId: i.variantId, quantity: i.quantity })) as Array<{
        variantId: string
        quantity: number
      }>
      const variantIds = dataLineItems.map((i) => i.variantId)
      const variantQuantity = {} as any
      for (const item of dataLineItems) {
        variantQuantity[item.variantId] = variantQuantity[item.variantId]
          ? variantQuantity[item.variantId] + item.quantity
          : item.quantity
      }
      const bundleProductDiscount = await ZnBundleProductDiscount.query()
        .where({ fastBundleId: fastBundleDiscountId })
        .preload('bundleProduct', (query) => {
          query.preload('items', (queryItem) => {
            queryItem.where({ ignoresDiscount: false }).preload('variants')
          })
        })
        .first()

      if (bundleProductDiscount?.bundleProduct) {
        if (bundleProductDiscount.bundleProduct.type === 'add_on') {
          // IS ADD_ON
          const validBundleVariantIds = bundleProductDiscount.bundleProduct?.items.flatMap((i) =>
            i.variants.map((v) => v.id)
          )
          // If the variantIds are not in the validBundleVariantIds, skip the bundle
          if (!variantIds.every((id) => validBundleVariantIds.includes(id))) {
            continue
          }
        } else {
          // IS OTHER BUNDLE
          // if (bundleProductDiscount.bundleProduct?.items?.length !== variantIds.length) {
          //   continue
          // }

          if (bundleProductDiscount.bundleProduct.itemType !== 'collection') {
            // change to modulo to account for duplicate bundles
            if (variantIds.length % bundleProductDiscount.bundleProduct?.items?.length !== 0) {
              continue
            }
          }
        }
        const variants = await ZnProductVariant.query().whereIn('id', variantIds)
        const subTotal = variants.reduce(
          (acc, variant) =>
            acc + Number(variant.price) * Number(variantQuantity?.[variant.id] || 0),
          0
        )
        const result = checkoutService.calculatorDiscountForVariant({
          discount: bundleProductDiscount,
          subTotal,
        })

        for (const variant of variants) {
          const findLineItemQuantity = dataLineItems
            .filter((i) => i.variantId === variant.id)
            .reduce((acc, item) => acc + item.quantity, 0)

          lineItems.push({
            quantity: findLineItemQuantity,
            variantId: variant.shopifyVariantId,
            appliedDiscount: result,
            customAttributes: [
              {
                key: 'fastBundleDiscountId',
                value: fastBundleDiscountId.toString(),
              },
            ],
          })
        }
      }
    }

    return lineItems
  }

  async createCart({ user, lineItems: defaultLineItems }: ICreateCartParams) {
    await user.load('addresses')
    const cartSectionIds = Array.from(new Set(defaultLineItems.map((item) => item?.cartSectionId)))
      .filter(Boolean)
      .join(',')

    // Get the default address
    const defaultAddress =
      user.addresses.find((address) => address.id === user.defaultAddressId) || user.addresses[0]
    // ------------------- NEW CODE --------------------

    const shippingAddress = {
      address1: defaultAddress?.address1 || '',
      address2: defaultAddress?.address2 || '',
      city: defaultAddress?.city || '',
      country: defaultAddress?.country || '',
      firstName: defaultAddress?.firstName || '',
      lastName: defaultAddress?.lastName || '',
      province: defaultAddress?.province || '',
      zip: defaultAddress?.zip || '',
    }
    const lineItems = await this.getLineItems({ lineItems: defaultLineItems })
    // Create the lines (LineItem)

    const draftOrderService = new ShopifyDraftOrderService()
    // create draft order
    const order = await draftOrderService.create({
      user,
      shippingAddress,
      billingAddress: shippingAddress,
      lineItems,
      discountCodes: [],
      note: '',
      tags: ['Zurno App'],
      customAttributes: [
        {
          key: 'cartSectionIds',
          value: cartSectionIds,
        },
      ],
    })

    const draftOrderId = order.id
    await draftOrderService.updateDraftOrderCustomAttributes({
      draftOrderId,
      customAttributes: [
        ...order.customAttributes,
        {
          key: 'cartSectionIds',
          value: cartSectionIds,
        },
        {
          key: 'draftOrderId',
          value: draftOrderId,
        },
      ],
    })

    // delete draft order after 10 minutes
    await ZnShopifyDraftOrder.create({
      shopifyId: draftOrderId,
      createdAt: DateTime.now().toUTC(),
      updatedAt: DateTime.now().toUTC(),
    })

    return formatOrderCart(order)
  }

  async updateCartLineItems(input: IDraftOrderUpdateLineItems) {
    const draftOrderService = new ShopifyDraftOrderService()
    return draftOrderService.updateDraftOrderLineItems(input)
  }

  async updateCartNote(input: IDraftOrderUpdateNote) {
    const draftOrderService = new ShopifyDraftOrderService()
    return draftOrderService.updateDraftOrderNote(input)
  }

  async updateCartDiscountCodes(input: IDraftOrderUpdateDiscountCodes) {
    const draftOrderService = new ShopifyDraftOrderService()
    return draftOrderService.updateDraftOrderDiscountCodes(input)
  }

  async getCart(id: string) {
    const draftOrderService = new ShopifyDraftOrderService()
    return draftOrderService.getDraftOrderById(id)
  }

  async updateCartAddress(data: IDraftOrderUpdateAddress) {
    const draftOrderService = new ShopifyDraftOrderService()

    return draftOrderService.updateDraftOrderAddress(data)
  }
}
