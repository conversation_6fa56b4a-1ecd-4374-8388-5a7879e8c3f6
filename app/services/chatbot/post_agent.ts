import {BaseChatbotAgent, EMPTY_MESSAGE_HOLDER} from "#services/chatbot/base_chatbot_agent"
import {EAIAssistantRole} from "#models/zn_ai_assistant"
import type {AgentInit, AgentResponse} from "#services/chatbot/chatbot_agent_interface"
import RecommendationEngine from "#services/chatbot/recommendation/recommendation_engine"
import {PostService} from "#services/post_service"
import ZnPost from "#models/zn_post"
import ZnUser from "#models/zn_user";
import {GeoService} from "../google/geo_service.js";

export default class PostAgent extends BaseChatbotAgent {
  readonly role = EAIAssistantRole.POST_ASSISTANT

  private postService = new PostService()
  private geoService = new GeoService()

  needsPreContext() {return true}

  async buildPreContext(init : AgentInit) {
    const user = await ZnUser.query()
      .where('id', init.userId)
      .preload('defaultAddress')
      .first()

    if (user && user.defaultAddress) {
      const address1 = user.defaultAddress.address1 ?? ''
      const city = user.defaultAddress.city ?? ''
      const province = user.defaultAddress.province ?? ''
      const country = user.defaultAddress.country
      return `User location: ${[address1, city, province, country].filter(Boolean).join(', ')}`
    }

    return `Cannot locate user's location`
  }


  constructor(assistantId: string, openAIAssistantId: string) {
    super(assistantId, openAIAssistantId)
  }

  needsPostProcess(firstResponse: AgentResponse){
    if (!firstResponse || typeof firstResponse !== 'object') return false
    const responseJSON = firstResponse as any
    return (responseJSON?.status === "draft_ready") && (responseJSON?.description || responseJSON?.draft_post)
  }

  async postProcess(firstResponse: AgentResponse, init: AgentInit) {
    const responseJSON = (firstResponse as any)
    console.log('Post agent resposneJSON', responseJSON)
    let locationConstraint = undefined
    if (responseJSON.description) {
      const description = (firstResponse as any).description

      if (responseJSON.location && responseJSON.location.name && responseJSON.location.radius ) {
        const radiusString = responseJSON.location.radius.trim()
        const radius = /^(\d+(?:\.\d+)?)\s*km$/.exec(radiusString)
        if (radius) {
          const radiusKM = parseFloat(radius[1])
          const latlon = await this.geoService.getLatLonFromAddress(responseJSON.location.name)
          if (latlon && latlon.latitude && latlon.longitude) {
            locationConstraint = {
              latitude: latlon.latitude,
              longitude: latlon.longitude,
              radius: radiusKM,
            }
          }
        }
        console.log('Location: ', responseJSON.location)
        console.log('Location constraint: ', locationConstraint)
      }

      const postRecommendation = await this.searchPost(description, locationConstraint)
      const recommendedTitles = await this.injectSearchContext(init.threadId, postRecommendation)
      const respondingText = await this.getRecommendationLines(recommendedTitles, init.userMessage)
      return {
        text: respondingText,
        questions: firstResponse.questions,
        productIds: [],
        collectionIds: [],
        postIds: postRecommendation,
        assistantId: this.openAIId
      }
    }

    if (responseJSON.draft_post) {
      console.log('Creating draft post')
      return this.createPost(responseJSON.draft_post, responseJSON.questions, init.userId)
    }

    return firstResponse

  }

  takeNextTurn(firstResponse: AgentResponse){
    if (!firstResponse || typeof firstResponse !== 'object') return false
    const responseJSON = firstResponse as any
    return responseJSON?.status === "in_conversation"
  }

  private async searchPost(description: string | string[], locationConstraint?: any) {
    const recommendationEngine = new RecommendationEngine()
    console.log('Searching posts with', description)
    const recommendation = await recommendationEngine.run({
      descriptions: {
        post : description
      },
      constraints: locationConstraint ? { post : {locationConstraint} } : undefined
    })
    return recommendation.post
  }

  private async createPost(
    draftPost: any,
    questions: string[],
    userId?: string
  ): Promise<AgentResponse> {
    try {
      console.log(draftPost)
      const createdPost = await this.postService.createPost(draftPost, userId)
      console.log(createdPost.id)
      return {
        text: EMPTY_MESSAGE_HOLDER,
        questions,
        productIds: [],
        collectionIds: [],
        postIds: [createdPost.id],
        assistantId: this.openAIId,
      }

    } catch (err) {
      console.error("PostAgent.createPost error:", err)
      return {
        text: "Sorry, I couldn’t save my post. Please try again.",
        questions,
        productIds: [],
        collectionIds: [],
        postIds: [],
        assistantId: this.openAIId,
      }
    }
  }

  private async injectSearchContext(
    threadId : string,
    postIds  : string[],
  ) {
    if (!postIds.length) {
      return []
    }

    const posts = await ZnPost
      .query()
      .whereIn('id', postIds)
      .select('title')

    const titles = posts.map(post => post.title?.trim()).filter(Boolean)
    const descriptions = posts.map(post => post.description?.trim()).filter(Boolean)

    await this.openai.beta.threads.messages.create(threadId, {
      role: 'assistant',
      content: [
        '### Recommended titles',
        ...titles,
        '### Recommendation descriptions',
        ...descriptions,
      ].join('\n'),
    })

    return titles
  }

  /**
   * Builds a ≤140-char sentence in the same language the user
   * just wrote, mentioning up to 3 post titles and the total count.
   */
  private async getRecommendationLines(
    titles: string[],
    userMessage: string
  ): Promise<string> {
    const sampleLang = userMessage.slice(0, 200)
    const preview    = titles.slice(0, 3).join('", "')
    const total      = titles.length

    const completion =
      await this.openai.chat.completions.create({
        model: 'gpt-4o-mini',
        temperature: 0.5,
        max_tokens: 50,
        messages: [
          {
            role: 'system',
            content:
              [
                'You are a marketplace assistant.',
                'Reply in the **same language** as the user sample.',
                'Make ONE friendly sentence (≤140 chars).',
                'Mention the provided title(s). Avoid IDs.',
              ].join(' ')
          },
          {
            role: 'user',
            content:
              [
                `User sample: "${sampleLang}. Answer in the same language as the user sample"`,
                total === 1
                  ? `There is 1 listing: "${preview}".`
                  : `There are ${total} listings. Some examples: "${preview}".`,
              ].join('\n')
          },
        ],
      })

    const content = completion.choices[0]?.message?.content ?? ''
    return content.trim()
  }

}
