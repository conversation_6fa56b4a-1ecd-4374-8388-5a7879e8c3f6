import type { HttpContext } from '@adonisjs/core/http'

import ZnCartItem from '#models/zn_cart_item'
import ZnCartSection from '#models/zn_cart_section'
import ZnProduct from '#models/zn_product'
import { ShopCartService } from '#services/shop/shop_cart_service'
import {
  cartAddAllItemsValidator,
  cartAddBundleValidator,
  cartAddItemValidator,
  deleteCartSectionValidator,
  updateQuantityItemValidator,
} from '#validators/app/cart/zurno_cart_validator'
import db from '@adonisjs/lucid/services/db'

export default class CartSectionController {
  /**
   * @detail
   * @tag Cart Section
   */
  public async index({ user, response, request }: HttpContext) {
    const { cartId, page = 1, limit = 10 } = request.qs()
    const shopCartService = new ShopCartService()
    const cart = await shopCartService.mergeCart({ userId: user?.id, cartId })

    const cartSections = await ZnCartSection.query()
      .where('cartId', cart.id)
      .preload('cartItems', (query) => {
        query.preload('discount').preload('variant').preload('product')
      })
      .preload('bundle')
      .orderBy('updatedAt', 'desc')
      .paginate(page, limit)

    return response.ok(cartSections)
  }

  /**
   * @addItemToCart
   * @tag Cart Section
   */
  public async addItemToCart({ request, response, user }: HttpContext) {
    const payload = await request.validateUsing(cartAddItemValidator)

    try {
      const shopCartService = new ShopCartService()
      const result = await shopCartService.addItemToCart({ ...payload, userId: user?.id })

      if (result && result.bundleId) {
        // Check if cart section has more than 1 item
        const existSection = await ZnCartSection.query()
          .where('cartId', result.cartId)
          .where('bundleId', result.bundleId)
          .whereNot('id', result.id)
          .first()

        if (existSection) {
          await shopCartService.updateQuantityCartSection({
            cartSection: existSection,
            quantity: Number(existSection.quantity) + Number(result.quantity),
          })

          await result.related('cartItems').query().delete()
          await result.delete()

          await existSection.refresh()
          await existSection.load('bundle')
          await existSection.load('cartItems')

          return response.created(existSection)
        }
      }

      return response.created(result)
    } catch (error) {
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @addBundleToCart
   * @tag Cart Section
   */
  public async addBundleToCart({ request, response, user }: HttpContext) {
    const payload = await request.validateUsing(cartAddBundleValidator)

    try {
      const shopCartService = new ShopCartService()
      const result = await shopCartService.addBundleToCart({ ...payload, userId: user?.id })

      return response.created(result)
    } catch (error) {
      return response.badRequest({ error: error.message })
    }
  }

  /**
   * @updateCartSectionQuantity
   * @tag Cart Section
   */
  public async updateQuantity({ request, response }: HttpContext) {
    // Update the quantity of a cart section
    const payload = await request.validateUsing(updateQuantityItemValidator)
    const cartSection = await ZnCartSection.query()
      .where('id', payload.cartSectionId)
      .preload('cartItems')
      .preload('bundle', (query) => {
        query.preload('items', (query) => {
          query.preload('product').preload('variants')
        })
        query.preload('discounts')
      })
      .first()
    if (!cartSection) {
      return response.status(404).json({ message: 'Cart item not found' })
    }

    const shopCartService = new ShopCartService()
    let result = await shopCartService.updateQuantityCartSection({
      cartSection,
      quantity: payload.quantity,
    })

    await result?.refresh()
    await result?.load('bundle')
    await result?.load('cartItems')

    return response.ok(result)
  }

  /**
   * @deleteCartSection
   * @tag Cart Section
   */
  public async destroy({ request, response }: HttpContext) {
    const payload = await request.validateUsing(deleteCartSectionValidator)

    try {
      const cartSections = await ZnCartSection.query()
        .where('cartId', payload.cartId)
        .whereIn('id', payload.cartSectionIds)

      const cartSectionIds = cartSections.map((cs) => cs.id)

      await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      await ZnCartSection.query().whereIn('id', cartSectionIds).delete()

      return response.ok({ cartSectionIds })
    } catch (error) {
      return response.notFound({ message: 'Cart section not found' })
    }
  }

  /**
   * @clearCart
   * @tag Cart Section
   */
  public async clear({ request, response }: HttpContext) {
    const { cartId } = request.qs()
    if (!cartId) {
      return response.badRequest({ message: 'Cart ID is required' })
    }

    try {
      const cartSections = await ZnCartSection.query().where('cartId', cartId)

      const cartSectionIds = cartSections.map((cs) => cs.id)

      await ZnCartItem.query().whereIn('cartSectionId', cartSectionIds).delete()
      await ZnCartSection.query().whereIn('id', cartSectionIds).delete()

      return response.ok({ message: 'Cart section deleted successfully' })
    } catch (error) {
      return response.notFound({ message: 'Cart section not found' })
    }
  }

  /**
   * @count
   * @tag Cart Section
   */
  public async count({ params, response, request }: HttpContext) {
    const cartId = params.id
    const { bundleId, variantId } = request.qs()
    try {
      // Count bundle In Cart
      if (bundleId) {
        const cartSections = await ZnCartSection.query()
          .where('cartId', cartId)
          .where('bundleId', bundleId)
          .sum('quantity as count')
          .first()

        return response.ok({ totalQuantity: cartSections?.$extras.count || 0 })
      }

      // Count variant In Cart
      if (variantId) {
        const cartSections = await db.rawQuery(`
          SELECT SUM(cs.quantity * ci.quantity) as totalQuantity
          FROM
              zn_cart_sections cs
              LEFT JOIN zn_cart_items ci ON cs.id = ci.cartSectionId
          WHERE
              ci.variantId = '${variantId}'
              AND cs.cartId = '${cartId}'
        `)

        return response.ok({ totalQuantity: cartSections?.[0]?.[0]?.totalQuantity || 0 })
      }

      // Count all items In Cart
      const cartSections = await ZnCartSection.query()
        .where('cartId', cartId)
        .sum('quantity as count')
        .first()
      return response.ok({ count: cartSections?.$extras.count || 0 })
    } catch (error) {
      return response.notFound({ message: 'Cart section not found' })
    }
  }

  public async addAllItemsToCart({ request, response, user }: HttpContext) {
    const payload = await request.validateUsing(cartAddAllItemsValidator)
    const product = await ZnProduct.query()
      .where('id', payload.productId)
      .preload('variants', (query) => {
        query.where('availableForSale', true)
      })
      .firstOrFail()

    try {
      const shopCartService = new ShopCartService()
      const cartSections = []
      for (const variant of product.variants) {
        const cartSection = await shopCartService.addItemToCart({
          cartId: payload.cartId,
          variantId: variant.id,
          quantity: payload.quantity,
          userId: user?.id,
        })
        cartSections.push(cartSection)
      }

      return response.created({ cartId: cartSections?.[0]?.cartId, cartSections })
    } catch (error) {
      return response.badRequest({ error: error.message })
    }
  }
}
