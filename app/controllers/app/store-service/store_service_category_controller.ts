import { inject } from '@adonisjs/core'
import type { HttpContext } from '@adonisjs/core/http'
import ZnStoreServiceCategory from '#models/store_service/zn_store_service_category'
import { createCategoryValidator, updateCategoryValidator } from '#validators/store-service/index'

@inject()
export default class StoreServiceCategoryController {
  /**
   * @swagger
   * /api/v1/app/store-service/categories:
   *   get:
   *     tags:
   *       - Store Service Categories
   *     summary: List categories
   *     parameters:
   *       - name: storeId
   *         in: query
   *         required: true
   *         schema:
   *           type: string
   *       - name: page
   *         in: query
   *         schema:
   *           type: number
   *       - name: limit
   *         in: query
   *         schema:
   *           type: number
   *       - name: search
   *         in: query
   *         schema:
   *           type: string
   *       - name: priceFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: priceTo
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationFrom
   *         in: query
   *         schema:
   *           type: number
   *       - name: durationTo
   *         in: query
   *         schema:
   *           type: number
   */
  async index({ request, response }: HttpContext) {
    const {
      page = 1,
      limit = 10,
      storeId,
      search,
      priceFrom,
      priceTo,
      durationFrom,
      durationTo,
    } = await request.qs()

    const applyServiceFilters = (serviceQuery: any) => {
      if (priceFrom !== undefined) {
        serviceQuery.where('price', '>=', Number(priceFrom))
      }
      if (priceTo !== undefined) {
        serviceQuery.where('price', '<=', Number(priceTo))
      }
      if (durationFrom !== undefined) {
        serviceQuery.where('duration', '>=', Number(durationFrom))
      }
      if (durationTo !== undefined) {
        serviceQuery.where('duration', '<=', Number(durationTo))
      }
      if (search) {
        serviceQuery.whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
      }
    }

    const hasFilters =
      search ||
      priceFrom !== undefined ||
      priceTo !== undefined ||
      durationFrom !== undefined ||
      durationTo !== undefined

    let query = ZnStoreServiceCategory.query()
      .where('storeId', storeId)
      .orderBy('createdAt', 'desc') // Sort categories by newest first
      .preload('services', (serviceQuery) => {
        serviceQuery.preload('image').orderBy('createdAt', 'desc') // Sort services by newest first

        applyServiceFilters(serviceQuery)
      })

    if (search) {
      query.where((query) => {
        query
          .whereRaw('LOWER(name) LIKE LOWER(?)', [`%${search}%`])
          .orWhereHas('services', (serviceQuery) => {
            applyServiceFilters(serviceQuery)
          })
      })
    }

    // When filters are applied, ensure categories have at least one matching service
    if (hasFilters) {
      query.whereHas('services', (serviceQuery) => {
        applyServiceFilters(serviceQuery)
      })
    }

    const result = await query.paginate(page, limit)

    return response.ok(result.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/categories:
   *   post:
   *     tags:
   *       - Store Service Categories
   *     summary: Create a new category
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - storeId
   *             properties:
   *               name:
   *                 type: string
   *               storeId:
   *                 type: string
   */
  async store({ request, response }: HttpContext) {
    const payload = await createCategoryValidator.validate(request.all())

    const categoryData: any = {
      name: payload.name,
      storeId: payload.storeId,
    }
    if (payload.imageId && payload.imageId !== null && payload.imageId !== '') {
      categoryData.imageId = payload.imageId
    }

    const category = await ZnStoreServiceCategory.create(categoryData)

    return response.json(category)
  }

  /**
   * @swagger
   * /api/v1/app/store-service/categories/{id}:
   *   put:
   *     tags:
   *       - Store Service Categories
   *     summary: Update a category
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *             properties:
   *               name:
   *                 type: string
   */
  async update({ request, response, params }: HttpContext) {
    const payload = await updateCategoryValidator.validate(request.all())
    const category = await ZnStoreServiceCategory.findOrFail(params.id)

    const updateData: any = {}
    if (payload.name) {
      updateData.name = payload.name
    }
    if (payload.imageId && payload.imageId !== null && payload.imageId !== '') {
      updateData.imageId = payload.imageId
    } else if (payload.imageId === null || payload.imageId === '') {
      updateData.imageId = undefined
    }

    if (Object.keys(updateData).length > 0) {
      await category.merge(updateData).save()
    }

    return response.ok(category.serialize())
  }

  /**
   * @swagger
   * /api/v1/app/store-service/categories/{id}:
   *   delete:
   *     tags:
   *       - Store Service Categories
   *     summary: Delete a category
   *     parameters:
   *       - name: id
   *         in: path
   *         required: true
   *         schema:
   *           type: string
   */
  async destroy({ params, response }: HttpContext) {
    const category = await ZnStoreServiceCategory.findOrFail(params.id)
    await category.softDelete()
    return response.ok({
      message: 'Category deleted successfully',
    })
  }
}
