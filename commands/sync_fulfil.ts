import { BaseCommand } from '@adonisjs/core/ace';
import type { CommandOptions } from '@adonisjs/core/types/ace';

export default class SyncFulfilSuppliers extends BaseCommand {
  static commandName = 'sync:fulfil'
  static description = 'Sync data from fulfil data warehouse'

  static options: CommandOptions = {
    startApp: true,
  }

  async run() {
    try {

    } catch (error) {
      console.log(error.response.data);
    }
  }
}
